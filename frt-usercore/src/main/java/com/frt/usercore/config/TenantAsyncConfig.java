/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.config;

import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 支持租户上下文传递的异步配置
 * <p>
 * 配置Spring @Async注解自动传递租户上下文
 * </p>
 *
 * <AUTHOR>
 * @version TenantAsyncConfig.java, v 0.1 2025-08-28 14:00 wangyi
 */
@Slf4j
@Configuration
@EnableAsync
public class TenantAsyncConfig implements AsyncConfigurer {

    /**
     * 配置异步任务执行器
     * <p>
     * 使用自定义的TaskDecorator来传递租户上下文
     * </p>
     *
     * @return 异步任务执行器
     */
    @Override
    @Bean(name = "tenantTaskExecutor")
    public Executor getAsyncExecutor() {
        LogUtil.info(log, "TenantAsyncConfig.getAsyncExecutor >> 配置支持租户上下文的异步执行器");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 线程池配置
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("tenant-async-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        // 设置任务装饰器，自动传递租户上下文
        executor.setTaskDecorator(new TenantContextTaskDecorator());
        
        executor.initialize();
        
        LogUtil.info(log, "TenantAsyncConfig.getAsyncExecutor >> 租户异步执行器配置完成");
        
        return executor;
    }

    /**
     * 配置异步任务异常处理器
     *
     * @return 异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (throwable, method, objects) -> {
            String tenantId = TenantContextUtil.getTenantId();
            LogUtil.error(log, "TenantAsyncConfig >> 异步任务执行异常 >> tenantId = {}, method = {}, error = {}", 
                    tenantId, method.getName(), throwable.getMessage(), throwable);
        };
    }

    /**
     * 租户上下文任务装饰器
     * <p>
     * 在任务执行前自动设置租户上下文，执行后自动清理
     * </p>
     */
    public static class TenantContextTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            // 获取当前线程的租户ID
            String tenantId = TenantContextUtil.getTenantId();
            
            LogUtil.debug(log, "TenantContextTaskDecorator.decorate >> 装饰异步任务 >> tenantId = {}", tenantId);
            
            return () -> {
                try {
                    // 在异步线程中设置租户上下文
                    if (tenantId != null) {
                        TenantContextUtil.setTenantId(tenantId);
                        LogUtil.debug(log, "TenantContextTaskDecorator >> 异步任务开始执行 >> tenantId = {}", tenantId);
                    }
                    
                    // 执行原始任务
                    runnable.run();
                    
                } finally {
                    // 清理租户上下文
                    TenantContextUtil.clear();
                    LogUtil.debug(log, "TenantContextTaskDecorator >> 异步任务执行完成，上下文已清理");
                }
            };
        }
    }

    /**
     * 创建额外的线程池执行器（用于特定场景）
     *
     * @return 线程池执行器
     */
    @Bean(name = "tenantCustomExecutor")
    public Executor tenantCustomExecutor() {
        LogUtil.info(log, "TenantAsyncConfig.tenantCustomExecutor >> 配置自定义租户线程池");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 自定义线程池配置
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(30);
        executor.setThreadNamePrefix("tenant-custom-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        // 设置任务装饰器
        executor.setTaskDecorator(new TenantContextTaskDecorator());
        
        executor.initialize();
        
        LogUtil.info(log, "TenantAsyncConfig.tenantCustomExecutor >> 自定义租户线程池配置完成");
        
        return executor;
    }
}
