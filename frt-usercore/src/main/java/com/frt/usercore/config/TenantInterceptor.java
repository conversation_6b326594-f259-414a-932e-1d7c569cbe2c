/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.config;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.constants.base.BaseConstants;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 租户拦截器
 * <p>
 * 拦截所有HTTP请求，从请求头中获取租户ID并存储到线程上下文中，
 * 确保在整个请求处理过程中都能获取到正确的租户信息
 * </p>
 *
 * <AUTHOR>
 * @version TenantInterceptor.java, v 0.1 2025-08-28 10:30 wangyi
 */
@Slf4j
@Component
public class TenantInterceptor implements HandlerInterceptor {

    /**
     * 请求处理前的拦截方法
     * <p>
     * 从HTTP请求头中获取租户ID，并将其存储到线程上下文中
     * </p>
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  处理器对象
     * @return 是否继续处理请求，true表示继续，false表示中断
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) 
            throws Exception {
        
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        
        LogUtil.info(log, "TenantInterceptor.preHandle >> 开始处理请求 >> method = {}, uri = {}", 
                method, requestUri);
        
        try {
            // 从请求头中获取租户ID
            String tenantId = extractTenantId(request);
            
            if (StrUtil.isNotBlank(tenantId)) {
                // 验证租户ID格式
                // if (isValidTenantId(tenantId)) {
                //     // 设置租户上下文
                //     TenantContextUtil.setTenantId(tenantId);
                //     LogUtil.info(log, "TenantInterceptor.preHandle >> 租户上下文设置成功 >> tenantId = {}",
                //             tenantId);
                // } else {
                //     LogUtil.warn(log, "TenantInterceptor.preHandle >> 租户ID格式不合法 >> tenantId = {}",
                //             tenantId);
                //     // 格式不合法时使用默认租户ID
                //     TenantContextUtil.setTenantId(BaseConstants.TenantConstants.DEFAULT_TENANT_ID);
                // }

                // 设置租户上下文
                TenantContextUtil.setTenantId(tenantId);
                LogUtil.info(log, "TenantInterceptor.preHandle >> 租户上下文设置成功 >> tenantId = {}",
                        tenantId);
            } else {
                LogUtil.info(log, "TenantInterceptor.preHandle >> 请求头中未找到租户ID，使用默认租户ID");
                // 未提供租户ID时使用默认值
                TenantContextUtil.setTenantId(BaseConstants.TenantConstants.DEFAULT_TENANT_ID);
            }
            
            LogUtil.info(log, "TenantInterceptor.preHandle >> 请求预处理完成 >> " +
                    "finalTenantId = {}", TenantContextUtil.getTenantId());
            
            return true;
            
        } catch (Exception e) {
            LogUtil.error(log, "TenantInterceptor.preHandle >> 处理租户上下文时发生异常 >> error = {}", 
                    e.getMessage(), e);
            
            // 异常情况下设置默认租户ID，确保请求能够继续处理
            TenantContextUtil.setTenantId(BaseConstants.TenantConstants.DEFAULT_TENANT_ID);
            
            // 继续处理请求，不因为租户上下文设置失败而中断业务流程
            return true;
        }
    }

    /**
     * 请求处理完成后的清理方法
     * <p>
     * 清理线程上下文中的租户信息，防止内存泄漏
     * </p>
     *
     * @param request   HTTP请求对象
     * @param response  HTTP响应对象
     * @param handler   处理器对象
     * @param exception 处理过程中的异常（如果有）
     * @throws Exception 清理过程中可能抛出的异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception exception) throws Exception {
        
        String requestUri = request.getRequestURI();
        String tenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantInterceptor.afterCompletion >> 开始清理租户上下文 >> " +
                "uri = {}, tenantId = {}", requestUri, tenantId);
        
        try {
            // 清理租户上下文
            TenantContextUtil.clear();
            
            LogUtil.info(log, "TenantInterceptor.afterCompletion >> 租户上下文清理完成 >> uri = {}", 
                    requestUri);
            
        } catch (Exception e) {
            LogUtil.error(log, "TenantInterceptor.afterCompletion >> 清理租户上下文时发生异常 >> " +
                    "uri = {}, error = {}", requestUri, e.getMessage(), e);
        }
    }

    /**
     * 从HTTP请求中提取租户ID
     * <p>
     * 按优先级从不同位置获取租户ID：
     * 1. 请求头
     * 2. 请求参数（备用方案）
     * </p>
     *
     * @param request HTTP请求对象
     * @return 租户ID，可能为null或空字符串
     */
    private String extractTenantId(HttpServletRequest request) {
        // 优先从请求头获取
        String tenantId = request.getHeader(BaseConstants.TenantConstants.TENANT_ID_HEADER);
        
        if (StrUtil.isBlank(tenantId)) {
            // 备用方案：从请求参数获取
            tenantId = request.getParameter(BaseConstants.TenantConstants.TENANT_ID_HEADER);
        }
        
        LogUtil.debug(log, "TenantInterceptor.extractTenantId >> 提取租户ID >> tenantId = {}", tenantId);
        
        return StrUtil.trim(tenantId);
    }

    /**
     * 验证租户ID的格式是否合法
     * <p>
     * 检查租户ID是否符合业务规范：
     * 1. 长度在合理范围内
     * 2. 不包含特殊字符
     * </p>
     *
     * @param tenantId 待验证的租户ID
     * @return 如果格式合法返回true，否则返回false
     */
    private boolean isValidTenantId(String tenantId) {
        if (StrUtil.isBlank(tenantId)) {
            return false;
        }
        
        // 检查长度
        int length = tenantId.length();
        if (length < BaseConstants.TenantConstants.TENANT_ID_MIN_LENGTH || 
            length > BaseConstants.TenantConstants.TENANT_ID_MAX_LENGTH) {
            LogUtil.warn(log, "TenantInterceptor.isValidTenantId >> 租户ID长度不合法 >> " +
                    "tenantId = {}, length = {}", tenantId, length);
            return false;
        }
        
        // 检查字符合法性（只允许字母、数字、下划线、中划线）
        if (!tenantId.matches("^[a-zA-Z0-9_-]+$")) {
            LogUtil.warn(log, "TenantInterceptor.isValidTenantId >> 租户ID包含非法字符 >> tenantId = {}", 
                    tenantId);
            return false;
        }
        
        return true;
    }
}
