/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文工具类
 * <p>
 * 提供租户ID的线程级别存储和管理功能，确保在多租户环境下的数据隔离
 * 注意：当前版本仅支持同步操作，不支持异步任务中的上下文传递
 * </p>
 *
 * <AUTHOR>
 * @version TenantContextUtil.java, v 0.1 2025-08-28 10:00 wangyi
 */
@Slf4j
public class TenantContextUtil {

    /**
     * 租户ID的ThreadLocal存储
     * <p>
     * 使用普通ThreadLocal实现线程级别的租户ID存储
     * 仅支持同步操作，异步任务中无法获取到租户上下文
     * </p>
     */
    private static final ThreadLocal<String> TENANT_CONTEXT = new ThreadLocal<>();

    /**
     * 私有构造函数，防止实例化
     */
    private TenantContextUtil() {
        // 工具类不允许实例化
    }

    /**
     * 设置当前线程的租户ID
     * <p>
     * 将租户ID存储到当前线程的上下文中，用于后续的数据隔离操作
     * </p>
     *
     * @param tenantId 租户ID，不能为空
     * @throws IllegalArgumentException 当tenantId为空时抛出
     */
    public static void setTenantId(String tenantId) {
        LogUtil.info(log, "TenantContextUtil.setTenantId >> 设置租户ID >> tenantId = {}", tenantId);
        
        if (StrUtil.isBlank(tenantId)) {
            LogUtil.error(log, "TenantContextUtil.setTenantId >> 租户ID不能为空");
            throw new IllegalArgumentException("租户ID不能为空");
        }
        
        TENANT_CONTEXT.set(tenantId);
        LogUtil.info(log, "TenantContextUtil.setTenantId >> 租户ID设置成功 >> tenantId = {}", tenantId);
    }

    /**
     * 获取当前线程的租户ID
     * <p>
     * 从当前线程的上下文中获取租户ID，如果未设置则返回null
     * </p>
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static String getTenantId() {
        String tenantId = TENANT_CONTEXT.get();
        LogUtil.debug(log, "TenantContextUtil.getTenantId >> 获取租户ID >> tenantId = {}", tenantId);
        if (StrUtil.isBlank(tenantId)) {
            throw new IllegalArgumentException("请求头参数租户ID不能为空");
        }
        return tenantId;
    }

    /**
     * 检查当前线程是否已设置租户ID
     * <p>
     * 判断当前线程上下文中是否存在有效的租户ID
     * </p>
     *
     * @return 如果已设置有效的租户ID则返回true，否则返回false
     */
    public static boolean hasTenantId() {
        String tenantId = TENANT_CONTEXT.get();
        boolean hasId = StrUtil.isNotBlank(tenantId);
        LogUtil.debug(log, "TenantContextUtil.hasTenantId >> 检查租户ID是否存在 >> hasId = {}, tenantId = {}", 
                hasId, tenantId);
        return hasId;
    }

    /**
     * 清理当前线程的租户上下文
     * <p>
     * 从当前线程的上下文中移除租户ID，防止内存泄漏
     * 建议在请求处理完成后调用此方法进行清理
     * </p>
     */
    public static void clear() {
        String tenantId = TENANT_CONTEXT.get();
        LogUtil.info(log, "TenantContextUtil.clear >> 清理租户上下文 >> 清理前tenantId = {}", tenantId);
        
        TENANT_CONTEXT.remove();
        
        LogUtil.info(log, "TenantContextUtil.clear >> 租户上下文清理完成");
    }

    /**
     * 获取当前线程的租户ID，如果未设置则返回默认值
     * <p>
     * 提供一个带默认值的获取方法，避免返回null值
     * </p>
     *
     * @param defaultValue 默认租户ID值
     * @return 当前线程的租户ID，如果未设置则返回defaultValue
     */
    public static String getTenantIdOrDefault(String defaultValue) {
        String tenantId = TENANT_CONTEXT.get();
        String result = StrUtil.isNotBlank(tenantId) ? tenantId : defaultValue;

        LogUtil.debug(log, "TenantContextUtil.getTenantIdOrDefault >> 获取租户ID或默认值 >> " +
                "tenantId = {}, defaultValue = {}, result = {}", tenantId, defaultValue, result);

        return result;
    }
}
