/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import com.frt.usercore.common.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * 租户安全的异步任务工具类
 * <p>
 * 提供在异步任务中安全传递和使用租户上下文的工具方法
 * </p>
 *
 * <AUTHOR>
 * @version TenantAsyncUtil.java, v 0.1 2025-08-28 12:00 wangyi
 */
@Slf4j
public class TenantAsyncUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private TenantAsyncUtil() {
        // 工具类不允许实例化
    }

    /**
     * 在异步任务中安全执行操作（无返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param task 要执行的异步任务
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> runAsync(Runnable task) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantAsyncUtil.runAsync >> 开始异步任务 >> tenantId = {}", currentTenantId);
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 在异步线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.info(log, "TenantAsyncUtil.runAsync >> 异步任务执行中 >> tenantId = {}", currentTenantId);
                
                // 执行实际任务
                task.run();
                
            } finally {
                // 清理异步线程的租户上下文
                TenantContextUtil.clear();
                LogUtil.info(log, "TenantAsyncUtil.runAsync >> 异步任务完成，上下文已清理");
            }
        });
    }

    /**
     * 在异步任务中安全执行操作（有返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param task 要执行的异步任务
     * @param <T>  返回值类型
     * @return CompletableFuture<T>
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> task) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 开始异步任务 >> tenantId = {}", currentTenantId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 在异步线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 异步任务执行中 >> tenantId = {}", currentTenantId);
                
                // 执行实际任务并返回结果
                return task.get();
                
            } finally {
                // 清理异步线程的租户上下文
                TenantContextUtil.clear();
                LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 异步任务完成，上下文已清理");
            }
        });
    }

    /**
     * 使用指定线程池在异步任务中安全执行操作（无返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param task     要执行的异步任务
     * @param executor 指定的线程池
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> runAsync(Runnable task, Executor executor) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantAsyncUtil.runAsync >> 开始异步任务（指定线程池） >> tenantId = {}", currentTenantId);
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 在异步线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.info(log, "TenantAsyncUtil.runAsync >> 异步任务执行中（指定线程池） >> tenantId = {}", currentTenantId);
                
                // 执行实际任务
                task.run();
                
            } finally {
                // 清理异步线程的租户上下文
                TenantContextUtil.clear();
                LogUtil.info(log, "TenantAsyncUtil.runAsync >> 异步任务完成，上下文已清理（指定线程池）");
            }
        }, executor);
    }

    /**
     * 使用指定线程池在异步任务中安全执行操作（有返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param task     要执行的异步任务
     * @param executor 指定的线程池
     * @param <T>      返回值类型
     * @return CompletableFuture<T>
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> task, Executor executor) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 开始异步任务（指定线程池） >> tenantId = {}", currentTenantId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 在异步线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 异步任务执行中（指定线程池） >> tenantId = {}", currentTenantId);
                
                // 执行实际任务并返回结果
                return task.get();
                
            } finally {
                // 清理异步线程的租户上下文
                TenantContextUtil.clear();
                LogUtil.info(log, "TenantAsyncUtil.supplyAsync >> 异步任务完成，上下文已清理（指定线程池）");
            }
        }, executor);
    }

    /**
     * 包装Runnable任务，自动传递租户上下文
     * <p>
     * 适用于需要手动提交到线程池的场景
     * </p>
     *
     * @param task 原始任务
     * @return 包装后的任务
     */
    public static Runnable wrapRunnable(Runnable task) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        return () -> {
            try {
                // 在执行线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.debug(log, "TenantAsyncUtil.wrapRunnable >> 执行包装任务 >> tenantId = {}", currentTenantId);
                
                // 执行原始任务
                task.run();
                
            } finally {
                // 清理租户上下文
                TenantContextUtil.clear();
            }
        };
    }

    /**
     * 包装Supplier任务，自动传递租户上下文
     * <p>
     * 适用于需要手动提交到线程池的场景
     * </p>
     *
     * @param task 原始任务
     * @param <T>  返回值类型
     * @return 包装后的任务
     */
    public static <T> Supplier<T> wrapSupplier(Supplier<T> task) {
        String currentTenantId = TenantContextUtil.getTenantId();
        
        return () -> {
            try {
                // 在执行线程中设置租户上下文
                if (currentTenantId != null) {
                    TenantContextUtil.setTenantId(currentTenantId);
                }
                
                LogUtil.debug(log, "TenantAsyncUtil.wrapSupplier >> 执行包装任务 >> tenantId = {}", currentTenantId);
                
                // 执行原始任务并返回结果
                return task.get();
                
            } finally {
                // 清理租户上下文
                TenantContextUtil.clear();
            }
        };
    }
}
