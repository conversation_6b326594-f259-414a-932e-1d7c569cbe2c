/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import com.frt.usercore.common.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * 可传递的ThreadLocal工具类
 * <p>
 * 支持在异步任务中自动传递ThreadLocal变量
 * </p>
 *
 * <AUTHOR>
 * @version TransmittableThreadLocalUtil.java, v 0.1 2025-08-28 13:00 wangyi
 */
@Slf4j
public class TransmittableThreadLocalUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private TransmittableThreadLocalUtil() {
        // 工具类不允许实例化
    }

    /**
     * 可传递的ThreadLocal实现
     * <p>
     * 支持在父子线程间自动传递变量值
     * </p>
     *
     * @param <T> 存储的数据类型
     */
    public static class TransmittableThreadLocal<T> extends ThreadLocal<T> {
        
        /**
         * 获取当前线程的值，如果为空则尝试从父线程获取
         */
        @Override
        public T get() {
            T value = super.get();
            if (value == null) {
                // 如果当前线程没有值，尝试从传递的上下文中获取
                value = getFromTransmittedContext();
                if (value != null) {
                    super.set(value);
                }
            }
            return value;
        }

        /**
         * 从传递的上下文中获取值
         * 这里可以根据具体需求实现，比如从请求属性中获取
         */
        protected T getFromTransmittedContext() {
            // 子类可以重写此方法来实现特定的传递逻辑
            return null;
        }

        /**
         * 包装Runnable，自动传递ThreadLocal上下文
         */
        public Runnable wrap(Runnable runnable) {
            T currentValue = super.get();
            return () -> {
                T oldValue = super.get();
                try {
                    if (currentValue != null) {
                        super.set(currentValue);
                    }
                    runnable.run();
                } finally {
                    if (oldValue != null) {
                        super.set(oldValue);
                    } else {
                        super.remove();
                    }
                }
            };
        }

        /**
         * 包装Callable，自动传递ThreadLocal上下文
         */
        public <V> Callable<V> wrap(Callable<V> callable) {
            T currentValue = super.get();
            return () -> {
                T oldValue = super.get();
                try {
                    if (currentValue != null) {
                        super.set(currentValue);
                    }
                    return callable.call();
                } finally {
                    if (oldValue != null) {
                        super.set(oldValue);
                    } else {
                        super.remove();
                    }
                }
            };
        }

        /**
         * 包装Supplier，自动传递ThreadLocal上下文
         */
        public <V> Supplier<V> wrap(Supplier<V> supplier) {
            T currentValue = super.get();
            return () -> {
                T oldValue = super.get();
                try {
                    if (currentValue != null) {
                        super.set(currentValue);
                    }
                    return supplier.get();
                } finally {
                    if (oldValue != null) {
                        super.set(oldValue);
                    } else {
                        super.remove();
                    }
                }
            };
        }
    }

    /**
     * 包装CompletableFuture.runAsync，自动传递所有ThreadLocal上下文
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.runAsync(() -> {
            try {
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                runnable.run();
            } finally {
                TenantContextUtil.clear();
            }
        });
    }

    /**
     * 包装CompletableFuture.supplyAsync，自动传递所有ThreadLocal上下文
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                return supplier.get();
            } finally {
                TenantContextUtil.clear();
            }
        });
    }

    /**
     * 包装CompletableFuture.runAsync（指定线程池），自动传递所有ThreadLocal上下文
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable, Executor executor) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.runAsync(() -> {
            try {
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                runnable.run();
            } finally {
                TenantContextUtil.clear();
            }
        }, executor);
    }

    /**
     * 包装CompletableFuture.supplyAsync（指定线程池），自动传递所有ThreadLocal上下文
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier, Executor executor) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                return supplier.get();
            } finally {
                TenantContextUtil.clear();
            }
        }, executor);
    }
}
