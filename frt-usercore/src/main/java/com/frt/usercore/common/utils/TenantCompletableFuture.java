/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import com.frt.usercore.common.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * 支持租户上下文传递的CompletableFuture
 * <p>
 * 自动在异步任务中传递租户上下文，使用方式与原生CompletableFuture完全相同
 * </p>
 *
 * <AUTHOR>
 * @version TenantCompletableFuture.java, v 0.1 2025-08-28 13:30 wangyi
 */
@Slf4j
public class TenantCompletableFuture {

    /**
     * 私有构造函数，防止实例化
     */
    private TenantCompletableFuture() {
        // 工具类不允许实例化
    }

    /**
     * 异步执行任务（无返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * 使用方式：TenantCompletableFuture.runAsync(() -> { 业务逻辑 });
     * </p>
     *
     * @param runnable 要执行的任务
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 设置租户上下文
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                
                // 执行业务逻辑
                runnable.run();
                
            } finally {
                // 清理上下文
                TenantContextUtil.clear();
            }
        });
    }

    /**
     * 异步执行任务（有返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * 使用方式：TenantCompletableFuture.supplyAsync(() -> { return 结果; });
     * </p>
     *
     * @param supplier 要执行的任务
     * @param <T>      返回值类型
     * @return CompletableFuture<T>
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置租户上下文
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                
                // 执行业务逻辑并返回结果
                return supplier.get();
                
            } finally {
                // 清理上下文
                TenantContextUtil.clear();
            }
        });
    }

    /**
     * 使用指定线程池异步执行任务（无返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param runnable 要执行的任务
     * @param executor 指定的线程池
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable, Executor executor) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 设置租户上下文
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                
                // 执行业务逻辑
                runnable.run();
                
            } finally {
                // 清理上下文
                TenantContextUtil.clear();
            }
        }, executor);
    }

    /**
     * 使用指定线程池异步执行任务（有返回值）
     * <p>
     * 自动传递当前线程的租户上下文到异步任务中
     * </p>
     *
     * @param supplier 要执行的任务
     * @param executor 指定的线程池
     * @param <T>      返回值类型
     * @return CompletableFuture<T>
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier, Executor executor) {
        String tenantId = TenantContextUtil.getTenantId();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置租户上下文
                if (tenantId != null) {
                    TenantContextUtil.setTenantId(tenantId);
                }
                
                // 执行业务逻辑并返回结果
                return supplier.get();
                
            } finally {
                // 清理上下文
                TenantContextUtil.clear();
            }
        }, executor);
    }

    /**
     * 创建已完成的CompletableFuture
     * <p>
     * 与CompletableFuture.completedFuture()功能相同，保持API一致性
     * </p>
     *
     * @param value 完成值
     * @param <T>   值类型
     * @return 已完成的CompletableFuture
     */
    public static <T> CompletableFuture<T> completedFuture(T value) {
        return CompletableFuture.completedFuture(value);
    }

    /**
     * 创建失败的CompletableFuture
     * <p>
     * 与CompletableFuture.failedFuture()功能相同，保持API一致性
     * </p>
     *
     * @param ex  异常
     * @param <T> 值类型
     * @return 失败的CompletableFuture
     */
    public static <T> CompletableFuture<T> failedFuture(Throwable ex) {
        return CompletableFuture.failedFuture(ex);
    }

    /**
     * 等待所有任务完成
     * <p>
     * 与CompletableFuture.allOf()功能相同，保持API一致性
     * </p>
     *
     * @param cfs CompletableFuture数组
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> allOf(CompletableFuture<?>... cfs) {
        return CompletableFuture.allOf(cfs);
    }

    /**
     * 等待任意一个任务完成
     * <p>
     * 与CompletableFuture.anyOf()功能相同，保持API一致性
     * </p>
     *
     * @param cfs CompletableFuture数组
     * @return CompletableFuture<Object>
     */
    public static CompletableFuture<Object> anyOf(CompletableFuture<?>... cfs) {
        return CompletableFuture.anyOf(cfs);
    }
}
