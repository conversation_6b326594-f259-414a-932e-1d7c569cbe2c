package com.frt.usercore.common.constants.base;

/**
 * <AUTHOR>
 * @version BaseConstants.java, v 0.1 2025-08-27 14:48 wangyi
 */
public class BaseConstants {

    // 获取项目基础包名
    public static final String BASE_PACKAGE = BaseConstants.class.getPackage().getName();

    public static final String VALIDATE_ERROR_MSG_TEMPLATE = "数据校验失败，{}";

    /**
     * 租户相关常量
     */
    public static final class TenantConstants {

        /**
         * 租户ID请求头名称
         */
        public static final String TENANT_ID_HEADER = "TENANT_ID";

        /**
         * 默认租户ID
         */
        public static final String DEFAULT_TENANT_ID = "default";

        /**
         * 租户ID最大长度
         */
        public static final int TENANT_ID_MAX_LENGTH = 64;

        /**
         * 租户ID最小长度
         */
        public static final int TENANT_ID_MIN_LENGTH = 1;

        /**
         * 私有构造函数，防止实例化
         */
        private TenantConstants() {
            // 常量类不允许实例化
        }
    }
}